
import React, { useCallback, useState } from "react";
import React<PERSON>low, {
  addEdge,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  ReactFlowProvider,
  Handle,
  Position,
  SelectionMode,
} from "reactflow";
import "reactflow/dist/style.css";

const initialNodes = [];
const initialEdges = [];

const nodeTypes = {
  Router: ({ data }) => (
    <div style={{ padding: "10px", border: "1px solid black", borderRadius: "5px", background: "lightblue", textAlign: "center", position: "relative" }}>
      <Handle type="target" position={Position.Top} id="in-1" style={{ left: "50%" }} />
      
      <img src="icon/router-icon.png" alt="Router" style={{ width: "50px", height: "50px" }} />
      
      <Handle type="source" position={Position.Bottom} id="out-1" style={{ left: "12.5%" }} />
      <Handle type="source" position={Position.Bottom} id="out-2" style={{ left: "37.5%" }} />
      <Handle type="source" position={Position.Bottom} id="out-3" style={{ left: "62.5%" }} />
      <Handle type="source" position={Position.Bottom} id="out-4" style={{ left: "87.5%" }} />
    </div>
  ),
  
  Firewall: ({ data }) => (
    <div style={{ padding: "10px", border: "1px solid black", borderRadius: "5px", background: "lightcoral", textAlign: "center", position: "relative" }}>
      <Handle type="target" position={Position.Top} id="input" />
      
      <img src="icon/firewall-icon.png" alt="Firewall" style={{ width: "50px", height: "50px" }} />
      
      <Handle type="source" position={Position.Bottom} id="output" />
    </div>
  ),

  Switch: ({ data }) => (
    <div style={{ padding: "10px", border: "1px solid black", borderRadius: "5px", background: "lightgreen", textAlign: "center", position: "relative" }}>
      <Handle type="target" position={Position.Top} id="input" style={{ left: "50%" }} />
      
      <img src="icon/switch-icon.png" alt="Switch" style={{ width: "50px", height: "50px" }} />
      
      <Handle type="source" position={Position.Bottom} id="out-1" style={{ left: "12.5%" }} />
      <Handle type="source" position={Position.Bottom} id="out-2" style={{ left: "37.5%" }} />
      <Handle type="source" position={Position.Bottom} id="out-3" style={{ left: "62.5%" }} />
      <Handle type="source" position={Position.Bottom} id="out-4" style={{ left: "87.5%" }} />
    </div>
  ),

  VirtualMachine: ({ data }) => (
    <div style={{ padding: '10px', border: '1px solid black', borderRadius: '5px', background: 'lightgray', textAlign: 'center', position: 'relative' }}>
      <Handle type="target" position={Position.Top} id="input" />
      
      <img src="icon/virtualmachine-icon.png" alt="VirtualMachine" style={{ width: "50px", height: "50px" }} />
    </div>
  ),
};

export default function NetworkTopology() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [nodeCounts, setNodeCounts] = useState({ Router: 0, Firewall: 0, Switch: 0, VirtualMachine: 0 });
  const [selectedNode, setSelectedNode] = useState(null);
  const [sidebarVisible, setSidebarVisible] = useState(true);
  
  const [nodeConfig, setNodeConfig] = useState({
    Router: { ipAddress: "", subnetMask: "" },
    Firewall: { allowedPorts: "" },
    Switch: { vlans: "" },
    VirtualMachine: { osType: "" },
  });
  

  const onEdgeClick = (event, edge) => {
    event.stopPropagation();
    if (window.confirm("Are you sure you want to delete this edge?")) {
      setEdges((eds) => eds.filter((e) => e.id !== edge.id));
    }
  };


  const onConnect = useCallback((params) => {
    setEdges((eds) => addEdge({ 
      ...params, 
      animated: true,
      style: { strokeWidth: 3 } // Thicken the edge (default is 1)
    }, eds));
  }, [setEdges]);

  const onDrop = (event) => {
    event.preventDefault();
    const type = event.dataTransfer.getData("nodeType");
    const position = {
      x: event.clientX - (sidebarVisible ? 200 : 0), // Adjust for sidebar width
      y: event.clientY,
    };

    setNodeCounts((counts) => {
      const newCount = counts[type] + 1;
      const newNode = {
        id: `${type}-${newCount}`,
        type: type,
        data: { label: `${type} ${newCount}` },
        position,
      };
      setNodes((nds) => [...nds, newNode]);
      return { ...counts, [type]: newCount };
    });
  };

  const onDragStart = (event, type) => {
    event.dataTransfer.setData("nodeType", type);
  };

  const onNodeClick = (event, node) => {
    event.stopPropagation();
    setSelectedNode(node);
  };

  const deleteNode = () => {
    if (window.confirm(`Are you sure you want to delete ${selectedNode.data.label}?`)) {
      setNodes((nds) => nds.filter((n) => n.id !== selectedNode.id));
      setEdges((eds) => eds.filter((edge) => edge.source !== selectedNode.id && edge.target !== selectedNode.id));
      setSelectedNode(null); // Close panel after deleting
    }
  };

  const closeConfigPanel = () => setSelectedNode(null);

  const onInputChange = (event, nodeType) => {
    const { name, value } = event.target;
    setNodeConfig((prevConfig) => ({
      ...prevConfig,
      [nodeType]: {
        ...prevConfig[nodeType],
        [name]: value,
      },
    }));
  };

  const saveConfigToFile = () => {
    const configBlob = new Blob([JSON.stringify(nodeConfig)], { type: "application/json" });
    const url = URL.createObjectURL(configBlob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "network-configuration.json";
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <ReactFlowProvider>
      <div style={{ display: "flex", height: "100vh" }}>
        {sidebarVisible && (
          <div style={{ width: "200px", padding: "10px", background: "#f0f0f0", display: "flex", flexDirection: "column", alignItems: "center", transition: "0.3s" }}>
            {["Router", "Firewall", "Switch", "VirtualMachine"].map((type) => (
              <div
                key={type}
                draggable
                onDragStart={(event) => onDragStart(event, type)}
                style={{
                  cursor: "grab",
                  padding: "10px",
                  marginBottom: "10px",
                  fontSize: "24px",
                  border: "1px solid #ccc",
                  borderRadius: "5px",
                  background: "white",
                  textAlign: "center",
                  width: "100px",
                  height: "100px",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <img src={`icon/${type.toLowerCase()}-icon.png`} alt={type} style={{ width: "50px", height: "50px" }} />
              </div>
            ))}
          </div>
        )}
        
        {/* Button to Toggle Sidebar */}
        <button onClick={() => setSidebarVisible(!sidebarVisible)} style={{ position: "absolute", left: sidebarVisible ? "210px" : "10px", top: "10px", zIndex: 10, padding: "5px", background: "white", border: "1px solid #ccc", cursor: "pointer" }}>☰</button>

        <div style={{ flexGrow: 1 }} onDrop={onDrop} onDragOver={(event) => event.preventDefault()}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onEdgeClick={onEdgeClick}
            onNodeClick={onNodeClick}
            nodeTypes={nodeTypes}
            selectionMode={SelectionMode.Partial}
            fitView
          >
            <MiniMap />
            <Controls />
            <Background />
          </ReactFlow>
        </div>

        {/* Configuration Panel */}
        {selectedNode && (
          <div style={{ width: "300px", padding: "10px", background: "#f8f9fa", borderLeft: "1px solid #ccc", position: "absolute", right: "0", top: "0", height: "100vh", overflowY: "auto" }}>
            <h3>Configure {selectedNode.data.label}</h3>

            {selectedNode.type === "Router" && (
              <>
                <label>IP Address:</label>
                <input
                  type="text"
                  name="ipAddress"
                  value={nodeConfig.Router.ipAddress}
                  onChange={(e) => onInputChange(e, "Router")}
                />
                <label>Subnet Mask:</label>
                <input
                  type="text"
                  name="subnetMask"
                  value={nodeConfig.Router.subnetMask}
                  onChange={(e) => onInputChange(e, "Router")}
                />
              </>
            )}

            {selectedNode.type === "Firewall" && (
              <>
                <label>Allowed Ports:</label>
                <input
                  type="text"
                  name="allowedPorts"
                  value={nodeConfig.Firewall.allowedPorts}
                  onChange={(e) => onInputChange(e, "Firewall")}
                />
              </>
            )}

            {selectedNode.type === "Switch" && (
              <>
                <label>VLANs:</label>
                <input
                  type="text"
                  name="vlans"
                  value={nodeConfig.Switch.vlans}
                  onChange={(e) => onInputChange(e, "Switch")}
                />
              </>
            )}

            {selectedNode.type === "VirtualMachine" && (
              <>
                <label>OS Type:</label>
                <input
                  type="text"
                  name="osType"
                  value={nodeConfig.VirtualMachine.osType}
                  onChange={(e) => onInputChange(e, "VirtualMachine")}
                />
              </>
            )}

            <button onClick={deleteNode} style={{ marginTop: "10px", padding: "5px", background: "red", color: "white", border: "none", cursor: "pointer" }}>
              Delete Node
            </button>

            {/* Save Config Button */}
            <button onClick={saveConfigToFile} style={{ marginTop: "10px", padding: "5px", background: "green", color: "white", border: "none", cursor: "pointer" }}>
              Save Configuration
            </button>
          </div>
        )}
      </div>
    </ReactFlowProvider>
  );
}
